<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon
        :label-width="120">
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.策略名.label" :prop="formItemsConfig.策略名.prop">
              <Input v-model.trim="detailData[formItemsConfig.策略名.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.策略名.placeholder" :maxlength="formItemsConfig.策略名.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.策略描述.label" :prop="formItemsConfig.策略描述.prop">
              <Input v-model.trim="detailData[formItemsConfig.策略描述.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.策略描述.placeholder" :maxlength="formItemsConfig.策略描述.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.策略状态.label" :prop="formItemsConfig.策略状态.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.策略状态.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.策略状态.placeholder"
                :dicType="formItemsConfig.策略状态.dicType" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.协议.label" :prop="formItemsConfig.协议.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.协议.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.协议.placeholder"
                :dicType="formItemsConfig.协议.dicType" />
            </FormItem>
            </Col>

            <Col span="12">
            <FormItem :label="formItemsConfig.源IP.label" :prop="formItemsConfig.源IP.prop">
              <Input v-model.trim="detailData[formItemsConfig.源IP.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.源IP.placeholder" :maxlength="formItemsConfig.源IP.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.目的IP.label" :prop="formItemsConfig.目的IP.prop">
              <Input v-model.trim="detailData[formItemsConfig.目的IP.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.目的IP.placeholder" :maxlength="formItemsConfig.目的IP.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.源MAC.label" :prop="formItemsConfig.源MAC.prop">
              <Input v-model.trim="detailData[formItemsConfig.源MAC.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.源MAC.placeholder" :maxlength="formItemsConfig.源MAC.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.目的MAC.label" :prop="formItemsConfig.目的MAC.prop">
              <Input v-model.trim="detailData[formItemsConfig.目的MAC.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.目的MAC.placeholder" :maxlength="formItemsConfig.目的MAC.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.源端口.label" :prop="formItemsConfig.源端口.prop">
              <InputNumber v-model="detailData[formItemsConfig.源端口.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.源端口.placeholder" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.目的端口.label" :prop="formItemsConfig.目的端口.prop">
              <InputNumber v-model="detailData[formItemsConfig.目的端口.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.目的端口.placeholder" />
            </FormItem>
            </Col>


            <Col span="12">
            <FormItem :label="formItemsConfig.车载总线类型.label" :prop="formItemsConfig.车载总线类型.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.车载总线类型.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.车载总线类型.placeholder"
                :dicType="formItemsConfig.车载总线类型.dicType" />
            </FormItem>
            </Col>

            <Col span="12">
            <FormItem :label="formItemsConfig.动作.label" :prop="formItemsConfig.动作.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.动作.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.动作.placeholder"
                :dicType="formItemsConfig.动作.dicType" />
            </FormItem>
            </Col>

            <Col span="12">
            <FormItem :label="formItemsConfig.优先顺序.label" :prop="formItemsConfig.优先顺序.prop">
              <InputNumber v-model="detailData[formItemsConfig.优先顺序.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.优先顺序.placeholder" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.风险等级.label" :prop="formItemsConfig.风险等级.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.风险等级.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.风险等级.placeholder"
                :dicType="formItemsConfig.风险等级.dicType" />
            </FormItem>
            </Col>

          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';
import { createFirewallRule, getFirewallRuleDetail, updateFirewallRule } from '@/api/firewall-rule';
import LoadingDetail from '@/components/loading/loading-detail.vue';
import SelectDic from '@/components/select/select-dic-label.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import { LENGTH_MAX_INPUT } from '@/define';
import mixinsPageForm from '@/mixins/mixinsPageForm';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      brandList: [],
      modelList: [],
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getFirewallRuleDetail,
        },
        addDetailData: {
          apiFun: createFirewallRule,
          successMsg: '防火墙策略添加成功',
          replactRequestData: ({ requestData }) => {
            return {
              ...requestData,
              strategyId: this.$route.query.strategyId || this.$route.params.strategyId,
            };
          }
        },
        editDetailData: {
          apiFun: updateFirewallRule,
          successMsg: '防火墙策略修改成功',
          replactRequestData: ({ requestData }) => {
            return {
              ...requestData,
              strategyId: this.$route.query.strategyId || this.$route.params.strategyId,
            };
          }
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'firewall-list',
        },
      },
      // 页面配置-表单
    };
  },
  computed: {
    pageItemsConfig() {
      return {
        策略名: {
          editFormItem: {
            valuekey: 'ruleName',
            formRules: [{ ruleType: 'required' }, { ruleType: 'length', max: LENGTH_MAX_INPUT }],
          },
        },
        策略描述: {
          editFormItem: {
            valuekey: 'ruleDesc',
            formRules: [{ ruleType: 'required' }],
          },
        },
        策略状态: {
          editFormItem: {
            valuekey: 'status',
            formRules: [{ ruleType: 'required' }],
            dicType: 3008,
          },
        },
        协议: {
          editFormItem: {
            valuekey: 'protocol',
            formRules: [{ ruleType: 'required' }],
            dicType: 3007,
          },
        },
        源IP: {
          editFormItem: {
            valuekey: 'srcIp',
            formRules: [
              { ruleType: 'required', },
            ],
          },
        },
        目的IP: {
          editFormItem: {
            valuekey: 'dstIp',
            formRules: [
              { ruleType: 'required', },
            ],
          },
        },
        源MAC: {
          editFormItem: {
            valuekey: 'srcMac',
            type: 'date',
          },
        },
        目的MAC: {
          editFormItem: {
            valuekey: 'dstMac',
          },
        },
        源端口: {
          editFormItem: {
            valuekey: 'srcPort',
            formRules: [{ ruleType: 'required', validatorType: 'InputNumber', },],
          },
        },
        目的端口: {
          editFormItem: {
            valuekey: 'dstPort',
            formRules: [{ ruleType: 'required', validatorType: 'InputNumber', },],
          },
        },
        车载总线类型: {
          editFormItem: {
            valuekey: 'busType',
            formRules: [{ ruleType: 'required' }],
            dicType: '3009',
          },
        },
        动作: {
          editFormItem: {
            valuekey: 'action',
            formRules: [{ ruleType: 'required' }],
            dicType: '300B',
          },
        },
        优先顺序: {
          editFormItem: {
            valuekey: 'priority',
            formRules: [{ ruleType: 'required', validatorType: 'InputNumber', },],
          },
        },
        风险等级: {
          editFormItem: {
            valuekey: 'riskLevel',
            formRules: [{ ruleType: 'required' }],
            dicType: '300A',
          },
        },
      };
    }
  },
  created() {
    this.initDetailPage();
    // this.getBrand();
    // this.getModel();

  },
  mounted() {
    this.detailData.strategyId = this.$route.query.strategyId || this.$route.params.strategyId;
  },
  methods: {
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach(item => {
          item.label = item.id;
          item.remark = item.name;
        });
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach(item => {
          item.label = item.id;
          item.remark = item.modelName;
        });
      }
    },
    handleChange(e) {
      this.detailData.effectiveTime = e;
    },
    handleStrategyChange(strategy) {
      if (strategy === 'recurring') {
        this.detailData.effectiveTime = [];
      } else if (strategy === 'once') {
        this.detailData.effectiveTime = '';
      }
    },
    // handleTimeRangeChange(value) {
    //   console.log('Time range changed:', value);
    //   if (Array.isArray(value) && value.length === 2) {
    //     this.detailData.effectiveTime = value.join('-');
    //   } else {
    //     this.detailData.effectiveTime = '';
    //   }
    // }
  }
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
