<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon
        :label-width="120">
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.策略名.label" :prop="formItemsConfig.策略名.prop">
              <Input v-model.trim="detailData[formItemsConfig.策略名.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.策略名.placeholder" :maxlength="formItemsConfig.策略名.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.版本号.label" :prop="formItemsConfig.版本号.prop">
                <Input v-model.trim="detailData[formItemsConfig.版本号.prop]" :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.版本号.placeholder" :maxlength="formItemsConfig.版本号.maxlength" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.汽车品牌.label" :prop="formItemsConfig.汽车品牌.prop">
              <SelectDic v-model="detailData[formItemsConfig.汽车品牌.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.汽车品牌.placeholder" :list="formItemsConfig.汽车品牌.list" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.汽车型号.label" :prop="formItemsConfig.汽车型号.prop">
              <SelectDic v-model="detailData[formItemsConfig.汽车型号.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.汽车型号.placeholder" :list="formItemsConfig.汽车型号.list" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.策略状态.label" :prop="formItemsConfig.策略状态.prop">
              <SelectDic v-model="detailData[formItemsConfig.策略状态.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.策略状态.placeholder"
                :dicType="formItemsConfig.策略状态.dicType" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.生效时间.label" :prop="formItemsConfig.生效时间.prop">
              <DatePicker v-model="detailData[formItemsConfig.生效时间.prop]" :disabled="detailFormConfig.isReadonly"
                type="datetime" :format="'yyyy-MM-dd HH:mm:ss'" :editable="false" placement="right"
                :placeholder="formItemsConfig.生效时间.placeholder" @on-change="handleChange" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.策略优先级.label" :prop="formItemsConfig.策略优先级.prop">
              <InputNumber v-model="detailData[formItemsConfig.策略优先级.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.策略优先级.placeholder" />
            </FormItem>
            </Col>

          </Row>
          <Row>
            <Col span="24">
            <FormItem :label="formItemsConfig.描述.label" :prop="formItemsConfig.描述.prop">
              <Input v-model.trim="detailData[formItemsConfig.描述.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.描述.placeholder" type="textarea" :rows="2"
                :maxlength="formItemsConfig.描述.maxlength" show-word-limit />
            </FormItem>
            </Col>
          </Row>
        </Card>

        <Card title="检测规则" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.规则名称_事件类型_.label" :prop="formItemsConfig.规则名称_事件类型_.prop">
                <Select v-model="detailData[formItemsConfig.规则名称_事件类型_.prop]" filterable
                  :placeholder="formItemsConfig.规则名称_事件类型_.placeholder" @on-change="handleRuleChange">
                  <Option v-for="item in snortRuleList" :value="item.id" :key="item.id">{{ item.remark }}</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.规则内容.label" :prop="formItemsConfig.规则内容.prop">
              <Input v-model.trim="detailData[formItemsConfig.规则内容.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.规则内容.placeholder" :maxlength="formItemsConfig.规则内容.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.协议.label" :prop="formItemsConfig.协议.prop">
              <SelectDic v-model="detailData[formItemsConfig.协议.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.协议.placeholder"
                :dicType="formItemsConfig.协议.dicType" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.源对象.label" :prop="formItemsConfig.源对象.prop">
              <Input v-model.trim="detailData[formItemsConfig.源对象.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.源对象.placeholder" :maxlength="formItemsConfig.源对象.maxlength" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.目的对象.label" :prop="formItemsConfig.目的对象.prop">
              <Input v-model.trim="detailData[formItemsConfig.目的对象.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.目的对象.placeholder" :maxlength="formItemsConfig.目的对象.maxlength" />
            </FormItem>
            </Col>
          </Row>
        </Card>

        <Card title="告警与响应" dis-hover :bordered="false">
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.告警级别.label" :prop="formItemsConfig.告警级别.prop">
              <SelectDic v-model="detailData[formItemsConfig.告警级别.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.告警级别.placeholder"
                :dicType="formItemsConfig.告警级别.dicType" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.响应动作.label" :prop="formItemsConfig.响应动作.prop">
              <SelectDic v-model="detailData[formItemsConfig.响应动作.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.响应动作.placeholder"
                :dicType="formItemsConfig.响应动作.dicType" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12" v-if="detailData[formItemsConfig.响应动作.prop] === 'rate_limit'">
              <FormItem :label="formItemsConfig.限流阈值.label" :prop="formItemsConfig.限流阈值.prop">
                <Input v-model.trim="detailData[formItemsConfig.限流阈值.prop]" :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.限流阈值.placeholder" :maxlength="formItemsConfig.限流阈值.maxlength" />
              </FormItem>
            </Col>
            <Col span="12" v-if="detailData[formItemsConfig.响应动作.prop] === 'capture'">
            <FormItem :label="formItemsConfig.取证时长.label" :prop="formItemsConfig.取证时长.prop">
              <Input v-model.trim="detailData[formItemsConfig.取证时长.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.取证时长.placeholder" :maxlength="formItemsConfig.取证时长.maxlength" />
            </FormItem>
            </Col>
          </Row>
        </Card>

        <Card title="车载场景" dis-hover :bordered="false">
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.网络域.label" :prop="formItemsConfig.网络域.prop">
              <SelectDic v-model="detailData[formItemsConfig.网络域.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.网络域.placeholder"
                :dicType="formItemsConfig.网络域.dicType" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.设备信任等级.label" :prop="formItemsConfig.设备信任等级.prop">
              <SelectDic v-model="detailData[formItemsConfig.设备信任等级.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.设备信任等级.placeholder"
                :dicType="formItemsConfig.设备信任等级.dicType" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.总线负载阈值.label" :prop="formItemsConfig.总线负载阈值.prop">
              <InputNumber v-model="detailData[formItemsConfig.总线负载阈值.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.总线负载阈值.placeholder" :min="1"
                :max="100" />
            </FormItem>
            </Col>

          </Row>

        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import SelectDic from '@/components/select/select-dic-label.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import {
  Select as ISelect,
  Option,
  InputNumber
} from 'view-ui-plus';

import {
  createIntrusionSnort,
  getAllSnortRules,
  getIntrusionSnortDetail,
  updateIntrusionSnort
} from '@/api/intrusion-snort';

import {
  LENGTH_MAX_INPUT,
  LENGTH_MAX_INPUT_LONG
} from '@/define';
import mixinsPageForm from '@/mixins/mixinsPageForm';
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';

export default {
  mixins: [mixinsPageForm],
  components: {
    LoadingDetail,
    TypicalPageHeader,
    SelectDic,
    Select: ISelect,
    Option,
    InputNumber
  },
  data() {
    return {
      // 通用-表单
      brandList: [],
      modelList: [],
      snortRuleList: [],
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getIntrusionSnortDetail,
        },
        addDetailData: {
          apiFun: createIntrusionSnort,
          successMsg: '入侵检测规则添加成功',
          replactRequestData: ({ requestData }) => ({
            ...requestData,
            ruleName: this.detailData.ruleName,
          })
        },
        editDetailData: {
          apiFun: updateIntrusionSnort,
          successMsg: '入侵检测规则修改成功',
          replactRequestData: ({ requestData }) => ({
            ...requestData,
            ruleName: this.detailData.ruleName,
          })
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'intrusion-snort-list',
        },
      },
      // 页面配置-表单
      // pageItemsConfig: ,
    };
  },
  computed: {
    pageItemsConfig() {
      return {
        // --- 基础信息 ---
        策略名: {
          editFormItem: {
            valuekey: 'snortName',
            formRules: [{ ruleType: 'required' }, { ruleType: 'length', max: LENGTH_MAX_INPUT }],
          },
        },
        版本号: {
          editFormItem: {
            valuekey: 'version',
            formRules: [{ ruleType: 'required' }],
          },
        },
        汽车品牌: {
          editFormItem: {
            valuekey: 'brandId',
            list: this.brandList,
            formRules: [{ ruleType: 'required' }],
          },
        },
        汽车型号: {
          editFormItem: {
            valuekey: 'carModelId',
            list: this.modelList,
            formRules: [{ ruleType: 'required' }],
          },
        },
        策略状态: {
          editFormItem: {
            valuekey: 'status',
            formRules: [
              { ruleType: 'required', },
            ],
            dicType: '3008',
          },
        },
        生效时间: {
          editFormItem: {
            valuekey: 'effectiveTime',
            formRules: [{ ruleType: 'required', }],
            type: 'date',
          },
        },
        策略优先级: {
          editFormItem: {
            valuekey: 'priority',
            formRules: [
              {
                ruleType: 'required',
                validatorType: 'InputNumber',
              },
            ],
            initValue: 1
          },
        },
        描述: {
          editFormItem: {
            valuekey: 'description',
            formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }],
          },
        },

        // --- 检测规则 ---
        规则名称_事件类型_: {
          editFormItem: {
            valuekey: 'ruleId',
            formRules: [{ ruleType: 'required' }],
          },
        },
        规则内容: {
          editFormItem: {
            valuekey: 'ruleContent',
            formRules: [{ ruleType: 'required' }],
          },
        },
        协议: {
          editFormItem: {
            valuekey: 'protocol',
            formRules: [{ ruleType: 'required' }],
            dicType: '300C',
          },
        },
        源对象: {
          editFormItem: {
            valuekey: 'srcObj',
            // formRules: [{ ruleType: 'required' }],
          },
        },
        目的对象: {
          editFormItem: {
            valuekey: 'dstObj',
            // formRules: [{ ruleType: 'required' }],
          },
        },
        // --- 告警与响应 ---
        告警级别: {
          editFormItem: {
            valuekey: 'riskLevel',
            formRules: [{ ruleType: 'required' }],
            dicType: '300A'
          },
        },
        响应动作: {
          editFormItem: {
            valuekey: 'responseAction',
            formRules: [{ ruleType: 'required' }],
            dicType: '300F',
          },
        },
        限流阈值: {
          editFormItem: {
            valuekey: 'rateLimit',
            formRules: [{ ruleType: 'required', validatorType: 'InputStrNumber' }],
          },
        },
        取证时长: {
          editFormItem: {
            valuekey: 'captureDuration',
            formRules: [{ ruleType: 'required', validatorType: 'InputStrNumber' }],
          },
        },
        // --- 车载场景 ---
        网络域: {
          editFormItem: {
            valuekey: 'networkDomain',
            formRules: [{ ruleType: 'required' }],
            dicType: '300D',
          },
        },
        设备信任等级: {
          editFormItem: {
            valuekey: 'deviceTrustLevel',
            formRules: [{ ruleType: 'required' }],
            dicType: '300E',
          },
        },
        总线负载阈值: {
          editFormItem: {
            valuekey: 'busLoadThreshold',
            formRules: [{ ruleType: 'num', min: 1, max: 100 }],
          },
        },
      };
    }
  },
  created() {
    this.initDetailPage();
    this.getBrand();
    this.getModel();
    this.getSnortRules();
  },
  methods: {
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach(item => {
          item.label = item.id;
          item.remark = item.name;
        });
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach(item => {
          item.label = item.id;
          item.remark = item.modelName;
        });
      }
    },
    async getSnortRules() {
      const res = await getAllSnortRules({});
      if (res && res.length) {
        this.snortRuleList = res;
        this.snortRuleList.forEach(item => {
          item.label = item.id;
          item.remark = item.ruleName;
        });
      }
    },
    handleChange(e) {
      this.detailData.effectiveTime = e;
    },
    handleRuleChange(ruleId) {
      console.log(ruleId);
      const rule = this.snortRuleList.find(item => item.id === ruleId);
      console.log(rule);
      if (rule) {
        this.detailData.ruleContent = rule.ruleContent;
        this.detailData.protocol = rule.protocol;
        this.detailData.srcObj = rule.srcObj;
        this.detailData.dstObj = rule.dstObj;
        this.detailData.ruleName = rule.ruleName;
      }
    },
  }
};
</script>

<style lang="less" scoped>
@import '@/styles/form-page.less';

</style>