<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form
        v-show="!isLoadingGetDetail"
        ref="detailForm"
        :model="detailData"
        :rules="detailFormRules"
        label-colon
        :label-width="120"
      >
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.告警名称.label"
                :prop="formItemsConfig.告警名称.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.告警名称.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.告警级别.label"
                :prop="formItemsConfig.告警级别.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.告警级别.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.告警级别.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.来源类型.label"
                :prop="formItemsConfig.来源类型.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.来源类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.来源类型.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.具体来源.label"
                :prop="formItemsConfig.具体来源.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.具体来源.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.具体来源.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.对象类型.label"
                :prop="formItemsConfig.对象类型.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.对象类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.对象类型.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.对象.label" :prop="formItemsConfig.对象.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.对象.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.是否评估.label"
                :prop="formItemsConfig.是否评估.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.是否评估.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.是否评估.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.处理结果.label"
                :prop="formItemsConfig.处理结果.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.处理结果.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.处理结果.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.处理人.label" :prop="formItemsConfig.处理人.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.处理人.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card title="上报信息" dis-hover :bordered="false" v-show="detailData['sourceDetail'] == 5">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.上报人.label" :prop="formItemsConfig.上报人.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.上报人.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.联系方式.label"
                :prop="formItemsConfig.联系方式.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.联系方式.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.告警详情.label"
                :prop="formItemsConfig.告警详情.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.告警详情.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card
          title="源攻击信息"
          dis-hover
          :bordered="false"
          v-show="detailData['sourceDetail'] == 1"
        >
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.源IP.label" :prop="formItemsConfig.源IP.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.源IP.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.攻击次数.label"
                :prop="formItemsConfig.攻击次数.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.攻击次数.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card
          title="多车关联信息"
          dis-hover
          :bordered="false"
          v-show="detailData['sourceDetail'] == 2"
        >
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.事件类型.label"
                :prop="formItemsConfig.事件类型.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.事件类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="攻击次数" :prop="formItemsConfig.攻击次数2.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.攻击次数2.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card
          :title="`复杂分析信息${detailData['sourceDetail']}`"
          dis-hover
          :bordered="false"
          v-show="detailData['sourceDetail'] == 1"
        >
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          ></Table>
        </Card>
        <Card title="派发处置" dis-hover :bordered="false" v-show="detailData['status'] == 2">
          <CheckboxGroup v-model="detailData['nodeName']">
            <Checkbox label="防火墙策略"></Checkbox>
            <Checkbox label="上报情报源平台"></Checkbox>
            <Checkbox label="车企管理平台"></Checkbox>
          </CheckboxGroup>
        </Card>
        <Card
          title="防火墙策略配置"
          dis-hover
          :bordered="false"
          v-show="(detailData['nodeName'] || []).includes('防火墙策略')"
        >
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.规则名.label" :prop="formItemsConfig.规则名.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.规则名.prop]"
                  :placeholder="formItemsConfig.规则名.placeholder"
                  :maxlength="formItemsConfig.规则名.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.车辆品牌.label"
                :prop="formItemsConfig.车辆品牌.prop"
              >
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.车辆品牌.prop]"
                  :placeholder="formItemsConfig.车辆品牌.placeholder"
                  :dicType="formItemsConfig.车辆品牌.dicType"
                  :list="formItemsConfig.车辆品牌.list"
                  @change-data="changeBrand"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.车型.label" :prop="formItemsConfig.车型.prop">
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.车型.prop]"
                  :placeholder="formItemsConfig.车型.placeholder"
                  :dicType="formItemsConfig.车型.dicType"
                  :list="formItemsConfig.车型.list"
                />
              </FormItem>
            </Col>

            <Col span="12">
              <FormItem :label="formItemsConfig.类型.label" :prop="formItemsConfig.类型.prop">
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.类型.prop]"
                  :placeholder="formItemsConfig.类型.placeholder"
                  :dicType="formItemsConfig.类型.dicType"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.状态.label" :prop="formItemsConfig.状态.prop">
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.状态.prop]"
                  :placeholder="formItemsConfig.状态.placeholder"
                  :dicType="formItemsConfig.状态.dicType"
                />
              </FormItem>
            </Col>

            <Col span="12">
              <FormItem
                :label="formItemsConfig.生效策略.label"
                :prop="formItemsConfig.生效策略.prop"
              >
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.生效策略.prop]"
                  :placeholder="formItemsConfig.生效策略.placeholder"
                  :dicType="formItemsConfig.生效策略.dicType"
                  @change-data="handleStrategyChange"
                />
              </FormItem>
            </Col>

            <Col
              span="12"
              v-show="['recurring', 'once'].includes(detailData[formItemsConfig.生效策略.prop])"
            >
              <FormItem
                :label="formItemsConfig.生效时间.label"
                :prop="formItemsConfig.生效时间.prop"
              >
                <TimePicker
                  v-show="detailData[formItemsConfig.生效策略.prop] === 'recurring'"
                  type="timerange"
                  format="HH:mm"
                  placement="bottom-end"
                  v-model="detailData[formItemsConfig.生效时间.prop]"
                />
                <Input
                  v-show="detailData[formItemsConfig.生效策略.prop] === 'once'"
                  v-model:modelValue="detailData[formItemsConfig.生效时间.prop]"
                  :placeholder="formItemsConfig.生效时间.placeholder"
                />
              </FormItem>
            </Col>

            <Col span="24">
              <FormItem label="生效条件">
                <Checkbox v-model="detailData[formItemsConfig.车辆熄火时.prop]">
                  车辆熄火时
                </Checkbox>
                <Checkbox v-model="detailData[formItemsConfig.高风险警告时.prop]">
                  高风险警告时
                </Checkbox>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.规则说明.label"
                :prop="formItemsConfig.规则说明.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.规则说明.prop]"
                  :placeholder="formItemsConfig.规则说明.placeholder"
                  type="textarea"
                  :rows="2"
                  :maxlength="formItemsConfig.规则说明.maxlength"
                  show-word-limit
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card
          title="情报源平台配置"
          dis-hover
          :bordered="false"
          v-show="(detailData['nodeName'] || []).includes('上报情报源平台')"
        >
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.情报源平台.label"
                :prop="formItemsConfig.情报源平台.prop"
              >
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.情报源平台.prop]"
                  :placeholder="formItemsConfig.情报源平台.placeholder"
                  :list="formItemsConfig.情报源平台.list"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card
          title="车企管理平台配置"
          dis-hover
          :bordered="false"
          v-show="(detailData['nodeName'] || []).includes('车企管理平台')"
        >
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.车企管理平台.label"
                :prop="formItemsConfig.车企管理平台.prop"
              >
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.车企管理平台.prop]"
                  :placeholder="formItemsConfig.车企管理平台.placeholder"
                  :list="formItemsConfig.车企管理平台.list"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card title="审批意见" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <Input
                v-model.trim="detailData[formItemsConfig.审批意见.prop]"
                type="textarea"
                :rows="3"
                :maxlength="1000"
                show-word-limit
              />
            </Col>
          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <Button type="default" @click="goBack">取消</Button>
        <template v-if="detailData.status == 1">
          <!-- 待研判 -->
          <Button type="error" @click="judges('4')">不处置</Button>
          <Button type="success" @click="judges('2')">处置</Button>
        </template>
        <template v-if="detailData.status == 2">
          <!-- 待派发 -->
          <Button type="error" @click="deal('回退', '1')">回退</Button>
          <Button type="success" @click="deal('派发', '3')">派发</Button>
        </template>
        <Button type="primary" @click="toFlow">流程信息</Button>
        <Button type="primary" @click="toRelation">关联事件</Button>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';
import mixinsPageList from '@/mixins/mixinsPageList';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import { relationItem } from '@/api/safety/warn-event-manage';
import { getDetail, distribute, judge } from '@/api/safety/emergency-work-order';

import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';

import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG } from '@/define';
import { Checkbox } from 'view-ui-plus';

const needEvaluationList = [
  {
    value: 1,
    label: '是',
  },
  {
    value: 2,
    label: '否',
  },
];

const evaluationResultList = [
  {
    value: 1,
    label: '生成工单',
  },
  {
    value: 2,
    label: '忽略',
  },
];

const alarmLevelList = [
  {
    value: 1,
    label: '高',
  },
  {
    value: 2,
    label: '中',
  },
  {
    value: 3,
    label: '低',
  },
];

export default {
  mixins: [mixinsPageForm, mixinsPageList],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getDetail,
        },
        getList: {
          apiFun: relationItem,
        },
      },
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'emergency-work-order-list',
        },
      },

      brandList: [],
      modelList: [],
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['事件id', '事件类型', '受攻击设备', '攻击时间', '攻击顺序'];
      return columns;
    },
    pageItemsConfig() {
      return {
        告警名称: {
          editFormItem: {
            valuekey: 'alarmName',
          },
        },
        告警级别: {
          editFormItem: {
            valuekey: 'alarmLevel',
            list: alarmLevelList,
          },
        },
        来源类型: {
          editFormItem: {
            valuekey: 'sourceType',
            list: [
              {
                value: 1,
                label: '内部规则',
              },
              {
                value: 2,
                label: '外部情报源',
              },
              {
                value: 3,
                label: '手工录入',
              },
            ],
          },
        },
        具体来源: {
          editFormItem: {
            valuekey: 'sourceDetail',
            list: [
              {
                value: '1',
                label: '攻击源分析',
              },
              {
                value: '2',
                label: '多车关联分析',
              },
              {
                value: '3',
                label: '复杂攻击分析',
              },
              {
                value: '4',
                label: '外部情报源',
              },
              {
                value: '5',
                label: '手工录入',
              },
            ],
          },
        },
        对象类型: {
          editFormItem: {
            valuekey: 'targetType',
            list: [
              {
                value: '1',
                label: '品牌',
              },
              {
                value: '2',
                label: '车型',
              },
              {
                value: '3',
                label: '车辆',
              },
            ],
          },
        },
        对象: {
          editFormItem: {
            valuekey: 'targetIds',
          },
        },
        是否评估: {
          editFormItem: {
            valuekey: 'needEvaluation',
            list: needEvaluationList,
          },
        },
        处理结果: {
          editFormItem: {
            valuekey: 'evaluationResult',
            list: evaluationResultList,
          },
        },
        处理人: {
          editFormItem: {
            valuekey: 'evaluatorId',
          },
        },
        上报人: {
          editFormItem: {
            valuekey: 'reporter',
          },
        },
        联系方式: {
          editFormItem: {
            valuekey: 'reporterContact',
          },
        },
        告警详情: {
          editFormItem: {
            valuekey: 'manualAlarmInfo',
          },
        },
        源IP: {
          editFormItem: {
            valuekey: 'sourceIp',
          },
        },
        攻击次数: {
          editFormItem: {
            valuekey: 'attackCount',
          },
        },
        事件类型: {
          editFormItem: {
            valuekey: 'eventTypeId',
          },
          listColumn: {
            valuekey: 'eventType',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        事件id: {
          listColumn: {
            valuekey: 'id',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        受攻击设备: {
          listColumn: {
            valuekey: 'attackedDevice',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        攻击时间: {
          listColumn: {
            valuekey: 'eventTime',
            minWidth: 120,
            renderType: 'YMDHMS',
          },
        },
        攻击顺序: {
          listColumn: {
            valuekey: 'attackOrder',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        规则名: {
          editFormItem: {
            valuekey: 'name',
            formRules: [{ ruleType: 'required' }, { ruleType: 'length', max: LENGTH_MAX_INPUT }],
          },
        },
        车辆品牌: {
          editFormItem: {
            valuekey: 'brandId',
            formRules: [{ ruleType: 'required' }],
            list: this.brandList,
          },
        },
        车型: {
          editFormItem: {
            valuekey: 'carModelId',
            formRules: [{ ruleType: 'required' }],
            list: this.modelList,
          },
        },
        类型: {
          editFormItem: {
            valuekey: 'type',
            formRules: [{ ruleType: 'required' }],
            dicType: 3005,
          },
        },
        状态: {
          editFormItem: {
            valuekey: 'status',
            formRules: [{ ruleType: 'required' }],
            dicType: 3004,
          },
        },
        生效策略: {
          editFormItem: {
            valuekey: 'effectiveStrategy',
            formRules: [{ ruleType: 'required' }],
            dicType: 3006,
          },
        },
        生效时间: {
          editFormItem: {
            valuekey: 'effectiveTime',
            // formRules: [{ ruleType: 'required' }],
            type: 'date',
          },
        },
        车辆熄火时: {
          editFormItem: {
            valuekey: 'effectivePoweroff',
          },
        },
        高风险警告时: {
          editFormItem: {
            valuekey: 'effectiveWarnning',
          },
        },
        规则说明: {
          editFormItem: {
            valuekey: 'description',
            formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }],
          },
        },

        情报源平台: {
          editFormItem: {
            valuekey: 'a',
            dicType: 3006,
          },
        },
        车企管理平台: {
          editFormItem: {
            valuekey: 'b',
            dicType: 3006,
          },
        },
        审批意见: {
          editFormItem: {
            valuekey: 'comments',
          },
        },
      };
    },
  },
  async mounted() {
    await this.getBrand();
    const { params } = this.$route;
    const replaceRequestData = (requestData) => ({
      ...requestData,
      alarmId: params.id,
    });
    this.pageConfigAPI.getList.replaceRequestData = replaceRequestData;

    relationItem({
      alarmId: params.id,
    }).then((res) => {
      this.listData = [res.data];
    });

    this.initDetailPage();
    this.initPage();
  },
  methods: {
    changeBrand(e) {
      if (e) {
        this.getModel();
      } else {
        this.modelList = [];
        this.detailData.carModelId = '';
      }
    },
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach((item) => {
          item.label = item.id;
          item.remark = item.name;
        });
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach((item) => {
          item.label = item.id;
          item.remark = item.modelName;
        });
      }
    },
    handleChange(e) {
      this.detailData.effectiveTime = e;
    },
    handleStrategyChange(strategy) {
      if (strategy === 'recurring') {
        this.detailData.effectiveTime = [];
      } else if (strategy === 'once') {
        this.detailData.effectiveTime = '';
      }
    },

    deal(nodeName, type) {
      distribute({
        id: this.$route.params.id,
        comments: this.detailData.comments,
        nodeName,
        type,
      }).then((res) => {
        this.$Message.success('操作成功');
        this.goBack();
      });
    },
    judges(type) {
      judge({
        id: this.$route.params.id,
        comments: this.detailData.comments,
        type,
      }).then((res) => {
        this.$Message.success('操作成功');
        this.goBack();
      });
    },

    toFlow() {
      this.$router.push({
        name: 'emergency-work-order-flow',
        params: {
          record: JSON.stringify({
            id: this.$route.params.id,
          }),
          isReadonly: true,
          toChildrenPage: true,
        },
      });
    },
    toRelation() {
      this.$router.push({
        name: 'warn-event-manage-relations',
        params: {
          record: JSON.stringify({
            alarmId: this.$route.params.alarmId,
          }),
          isReadonly: true,
          toChildrenPage: true,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
