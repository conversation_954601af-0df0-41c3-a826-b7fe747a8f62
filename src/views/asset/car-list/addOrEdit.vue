<template>
    <div>
      <typical-page-header :header-title="pageTitle" />
      <div class="form">
        <LoadingDetail :isLoading="isLoadingGetDetail" />
        <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
          <Card dis-hover :bordered="false">
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.品牌.label" :prop="formItemsConfig.品牌.prop">
                  <Select
                    clearable
                    v-model="detailData[formItemsConfig.品牌.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.品牌.placeholder"
                    @on-change="changeBrand"
                  >
                    <Option v-for="(item, index) in brandList" :key="index" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.车型.label" :prop="formItemsConfig.车型.prop">
                  <Select
                    clearable
                    v-model="detailData[formItemsConfig.车型.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.车型.placeholder"
                  >
                    <Option v-for="(item, index) in modelList" :key="index" :value="item.id">{{ item.modelName }}</Option>
                  </Select>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.车辆ID.label" :prop="formItemsConfig.车辆ID.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.车辆ID.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.车辆ID.placeholder"
                    :maxlength="formItemsConfig.车辆ID.maxlength"
                  />
                </FormItem>
              </Col>
            </Row>
          </Card>
        </Form>
        <FooterToolbar>
          <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
            <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
          </template>
          <template v-else>
            <Button type="default" @click="goBack">取消</Button>
            <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
          </template>
        </FooterToolbar>
      </div>
    </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import mixinsPageForm from '@/mixins/mixinsPageForm';
import { detailApi, addApi, editApi } from '@/api/asset/car-list';
import { LENGTH_MAX_INPUT } from '@/define';
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';

export default {
    mixins: [mixinsPageForm],
    components: { LoadingDetail, TypicalPageHeader },
    data() {
      return {
        brandList: [],
        modelList: [],
        selectedBrand: '', // 当前选中品牌
        // 通用-表单
        isLoadingGetDetail: true,
        detailData: {},
        detailFormConfig: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          getDetailData: {
            apiFun: detailApi,
          },
          addDetailData: {
            apiFun: addApi,
            successMsg: '车辆添加成功',
          },
          editDetailData: {
            apiFun: editApi,
            successMsg: '车辆修改成功',
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          goBack: {
            routerName: 'car-list',
          },
        }
      };
    },
    computed: {
      pageItemsConfig() {
        return {
          品牌: {
            editFormItem: {
              valuekey: 'brandId',
              formRules: [{
                ruleType: 'required',
                ruleMessage: '请选择品牌',
              }],
            }
          },
          车型: {
            editFormItem: {
              valuekey: 'carModelId',
              formRules: [{
                ruleType: 'required',
                ruleMessage: '请选择先选择品牌后再选择车型',
              }],
            }
          },
          车辆ID: {
            editFormItem: {
              valuekey: 'vin',
              formRules: [{
                ruleType: 'required',
                  ruleMessage: '请填写车辆ID',
                  ruleTrigger: 'blur',
              },
              { ruleType: 'length', max: LENGTH_MAX_INPUT }
            ],
            }
          }
        };
      }
    },
    created() {
      this.initDetailPage();
      this.getBrand();
    },
    
    methods: {
      // 在获取车辆详情后调用此方法，用于加载车型列表
      getDetailAfter(detailData) {
        // 如果是编辑模式且已存在品牌ID，则加载对应车型
        if (detailData.brandId) {
          this.selectedBrand = detailData.brandId;
          this.getModel();
        }
      },
      
      changeBrand(e) {
        this.selectedBrand = e;
        if (this.selectedBrand) {
          this.getModel();
        } else {
          this.modelList = [];
          this.detailData.carModelId = '';
        }
      },
      async getBrand() {
        const res = await getAllBrandApi({});
        if (res && res.length) {
          this.brandList = res;
        }
      },
      async getModel() {
        const res = await getAllModelsApi({
          brandId: this.selectedBrand
        });
        if (res && res.length) {
          this.modelList = res;
        }
      }
    }
  };
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
