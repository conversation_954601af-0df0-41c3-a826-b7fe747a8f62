/*
 * @Author: your name
 * @Date: 2020-11-30 17:13:23
 * @LastEditTime: 2021-01-18 10:48:20
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \biyi-workbench\src\api\config\url.dev.js
 */
import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';

export default {
  // cas服务地址
  casUrl: qiankunWindow.$casUrl || 'https://biyi-template-cas.ctbiyi.cn',
  // 文件地址
  fileUrl: qiankunWindow.$fileUrl || 'http://localhost:9006',
  // 基座接口地址
  baseUrl: qiankunWindow.$baseUrl || 'http://localhost:9006',
  // 后端接口前缀
  authAPIUrl: qiankunWindow.$authAPIUrl || '',
  prefixAPI: qiankunWindow.$prefixAPI || '',
  authAPIUrlBase: qiankunWindow.$authAPIUrlBase || '/doiov-system-auth',
  prefixAPIBase: qiankunWindow.$prefixAPIBase || '',

};
